{"swagger": "2.0", "info": {"description": "Backend server for ZIA Academy.", "title": "ZIA Academy App", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0"}, "host": "************:443", "basePath": "/api/", "paths": {"/admins": {"post": {"security": [{"BearerAuth": []}], "description": "Create a new admin user with role \"Admin\"", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admins"], "summary": "Create Admin User", "parameters": [{"description": "Admin user details", "name": "admin", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.AdminForCreate"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.User"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/chapters": {"get": {"security": [{"BearerAuth": []}], "description": "get chapters for a subject_id", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["chapters"], "summary": "Get Chapters", "parameters": [{"type": "integer", "description": "Subject ID", "name": "subject_id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Chapter"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "create new chapter", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["chapters"], "summary": "CreateChapters", "parameters": [{"description": "chapter details", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.ChapterForCreate"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Chapter"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/content": {"get": {"security": [{"BearerAuth": []}], "description": "get videos and studymaterial for given chapter", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["questions"], "summary": "Get Content", "parameters": [{"type": "integer", "description": "Chapter ID", "name": "chapter_id", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Content"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/courses": {"get": {"security": [{"BearerAuth": []}], "description": "get courses for the logged in user. Returns all courses for non-student users (admin, etc.) and filtered courses for students (free courses + enrolled paid courses)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["courses"], "summary": "Get Courses", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.CoursesByCategory"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "create new course", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["courses"], "summary": "CreateCourse", "parameters": [{"description": "course details", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CourseForCreate"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Course"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/courses/{course_id}/tests/{test_id}": {"post": {"security": [{"BearerAuth": []}], "description": "associate an existing test with an existing course", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["courses"], "summary": "Associate Test with Course", "parameters": [{"type": "integer", "description": "Course ID", "name": "course_id", "in": "path", "required": true}, {"type": "integer", "description": "Test ID", "name": "test_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/enroll/{course_id}": {"post": {"security": [{"BearerAuth": []}], "description": "enroll student in a course", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["students"], "summary": "EnrollInCourse", "parameters": [{"type": "integer", "description": "course ID to enroll in", "name": "course_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Student"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/formula-cards": {"get": {"security": [{"BearerAuth": []}], "description": "get all formula cards organized by subject", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["formula-cards"], "summary": "Get Formula Cards", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.FormulaCardsBySubject"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "create multiple formula cards for a subject", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["formula-cards"], "summary": "CreateFormulaCards", "parameters": [{"description": "formula cards details", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.FormulaCardsForCreate"}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.FormulaCard"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/login": {"post": {"description": "login with email and password", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["login"], "summary": "<PERSON><PERSON>", "parameters": [{"description": "user_email and password", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.Credentials"}}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/previous-year-papers": {"get": {"security": [{"BearerAuth": []}], "description": "get previous year papers filtered by exam type, sorted by year (descending)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["previous-year-papers"], "summary": "Get Previous Year Papers by <PERSON>am <PERSON>", "parameters": [{"type": "string", "description": "Exam Type (IIT-JEE or NEET)", "name": "exam_type", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.PreviousYearPaper"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "create multiple previous year papers in bulk", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["previous-year-papers"], "summary": "CreatePreviousYearPapers", "parameters": [{"description": "previous year papers details", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.PreviousYearPapersForCreate"}}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.PreviousYearPaper"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/questions": {"get": {"security": [{"BearerAuth": []}], "description": "get questions for given topic and difficulty", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["questions"], "summary": "Get Questions", "parameters": [{"type": "string", "description": "Topic name", "name": "topic", "in": "query", "required": true}, {"type": "string", "description": "Difficulty ()", "name": "difficulty", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Question"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "create new question", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["questions"], "summary": "CreateQuestion", "parameters": [{"description": "question details", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.QuestionForCreate"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Question"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/section-types": {"post": {"security": [{"BearerAuth": []}], "description": "create new section type", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["tests"], "summary": "CreateSectionType", "parameters": [{"description": "section type details", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.SectionTypeForCreate"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.SectionType"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/students": {"post": {"description": "create new student", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["students"], "summary": "CreateStudent", "parameters": [{"description": "student details", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.StudentForCreate"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.CreatedStudentResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/studymaterials": {"post": {"security": [{"BearerAuth": []}], "description": "add a new material", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["library"], "summary": "AddStudyMaterial", "parameters": [{"description": "material details", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.MaterialForCreate"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.StudyMaterial"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/subjects": {"get": {"security": [{"BearerAuth": []}], "description": "get subjects for logged in student", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["library"], "summary": "Get Subjects", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Subject"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "create new subject", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["subjects"], "summary": "CreateSubject", "parameters": [{"description": "subject details", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.SubjectForCreate"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Subject"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/test-responses": {"post": {"security": [{"BearerAuth": []}], "description": "Record student responses for all questions in a test", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["test-responses"], "summary": "RecordTestResponses", "parameters": [{"description": "test responses", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.TestResponsesForCreate"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.TestResponsesResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/test-responses/evaluate": {"post": {"security": [{"BearerAuth": []}], "description": "Evaluate all unevaluated responses for a specific test (Admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["test-responses"], "summary": "EvaluateTestResponses", "parameters": [{"description": "test evaluation request", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.TestEvaluationRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.TestEvaluationResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/test-responses/rankings/{test_id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get rankings for all students in a specific test", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["test-responses"], "summary": "GetTestRankings", "parameters": [{"type": "integer", "description": "Test ID", "name": "test_id", "in": "path", "required": true}, {"type": "integer", "description": "Limit number of results (default: 100)", "name": "limit", "in": "query"}, {"type": "integer", "description": "Offset for pagination (default: 0)", "name": "offset", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.TestRankingResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/test-responses/{test_id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get student responses for a specific test", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["test-responses"], "summary": "GetStudentTestResponses", "parameters": [{"type": "integer", "description": "Test ID", "name": "test_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.TestResponse"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/test-types": {"post": {"security": [{"BearerAuth": []}], "description": "create new test type", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["tests"], "summary": "CreateTestType", "parameters": [{"description": "test type details", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.TestTypeForCreate"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.TestType"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/tests": {"get": {"security": [{"BearerAuth": []}], "description": "get tests for the logged in user. Students see only tests from enrolled courses. Admins see all tests.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["tests"], "summary": "Get Tests", "parameters": [{"type": "boolean", "description": "Filter by active status (true/false)", "name": "active", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.Test"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "create new test of a given type", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["tests"], "summary": "CreateTest", "parameters": [{"description": "test details", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.TestForCreate"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Test"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/tests/{test_id}/questions": {"post": {"security": [{"BearerAuth": []}], "description": "add questions to a test", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["tests"], "summary": "AddQuestionsToTest", "parameters": [{"type": "integer", "description": "Test ID", "name": "test_id", "in": "path", "required": true}, {"description": "question IDs and section name", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.AddQuestionsToTestRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/topics": {"post": {"security": [{"BearerAuth": []}], "description": "create new topic for questions", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["questions"], "summary": "CreateTopic", "parameters": [{"description": "topic details", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.TopicForCreate"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Topic"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/users/password": {"post": {"security": [{"BearerAuth": []}], "description": "update password for logged in user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "UpdatePassword", "parameters": [{"description": "new password", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdatePassword"}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}, "/videos": {"post": {"security": [{"BearerAuth": []}], "description": "add a new video", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["library"], "summary": "AddVideo", "parameters": [{"description": "video details", "name": "item", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.VideoForCreate"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.Video"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/http.HTTPError"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/http.HTTPError"}}}}}}, "definitions": {"gorm.DeletedAt": {"type": "object", "properties": {"time": {"type": "string"}, "valid": {"description": "Valid is true if Time is not NULL", "type": "boolean"}}}, "http.HTTPError": {"type": "object", "properties": {"code": {"type": "integer", "example": 400}, "message": {"type": "string", "example": "status bad request"}}}, "models.AddQuestionsToTestRequest": {"type": "object", "required": ["question_ids", "section_name"], "properties": {"question_ids": {"type": "array", "items": {"type": "integer"}}, "section_name": {"type": "string"}}}, "models.AdminForCreate": {"type": "object", "required": ["email", "full_name", "password", "phone_number"], "properties": {"contact_address": {"type": "string"}, "email": {"type": "string"}, "full_name": {"type": "string"}, "password": {"type": "string", "minLength": 6}, "phone_number": {"type": "string"}}}, "models.Chapter": {"type": "object", "properties": {"createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "displayName": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "studyMaterials": {"type": "array", "items": {"$ref": "#/definitions/models.StudyMaterial"}}, "subject": {"$ref": "#/definitions/models.Subject"}, "subjectID": {"type": "integer"}, "updatedAt": {"type": "string"}, "videos": {"type": "array", "items": {"$ref": "#/definitions/models.Video"}}}}, "models.ChapterForCreate": {"type": "object", "properties": {"displayName": {"type": "string"}, "name": {"type": "string"}, "subjectName": {"type": "string"}}}, "models.Content": {"type": "object", "properties": {"chapterName": {"type": "string"}, "pdfs": {"type": "array", "items": {"$ref": "#/definitions/models.StudyMaterial"}}, "videos": {"type": "array", "items": {"$ref": "#/definitions/models.Video"}}}}, "models.Course": {"type": "object", "properties": {"courseType": {"type": "string"}, "createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "description": {"type": "string"}, "discount": {"type": "number"}, "durationInDays": {"type": "integer"}, "id": {"type": "integer"}, "isFree": {"type": "boolean"}, "name": {"type": "string"}, "price": {"type": "integer"}, "subjects": {"type": "array", "items": {"$ref": "#/definitions/models.Subject"}}, "tests": {"type": "array", "items": {"$ref": "#/definitions/models.Test"}}, "updatedAt": {"type": "string"}}}, "models.CourseForCreate": {"type": "object", "properties": {"courseType": {"type": "string"}, "description": {"type": "string"}, "discount": {"type": "number"}, "durationInDays": {"type": "integer"}, "isFree": {"type": "boolean"}, "name": {"type": "string"}, "price": {"type": "integer"}, "subjects": {"type": "array", "items": {"$ref": "#/definitions/models.SubjectForCreate"}}}}, "models.CourseWithPurchased": {"type": "object", "properties": {"courseType": {"type": "string"}, "description": {"type": "string"}, "discount": {"type": "number"}, "durationInDays": {"type": "integer"}, "isFree": {"type": "boolean"}, "name": {"type": "string"}, "price": {"type": "integer"}, "purchased": {"type": "boolean"}}}, "models.CoursesByCategory": {"type": "object", "properties": {"free_courses": {"type": "array", "items": {"$ref": "#/definitions/models.CoursesByType"}}, "paid_courses": {"type": "array", "items": {"$ref": "#/definitions/models.CoursesByType"}}}}, "models.CoursesByType": {"type": "object", "properties": {"course_type": {"type": "string"}, "courses": {"type": "array", "items": {"$ref": "#/definitions/models.CourseWithPurchased"}}}}, "models.CreatedStudentResponse": {"type": "object", "properties": {"createdStudent": {"$ref": "#/definitions/models.Student"}, "token": {"type": "string"}}}, "models.Credentials": {"type": "object", "properties": {"password": {"type": "string"}, "user_email": {"type": "string"}}}, "models.Difficulty": {"type": "object", "properties": {"createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "id": {"type": "integer"}, "name": {"type": "string"}, "questions": {"type": "array", "items": {"$ref": "#/definitions/models.Question"}}, "updatedAt": {"type": "string"}}}, "models.FormulaCard": {"type": "object", "properties": {"createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "id": {"type": "integer"}, "imageUrl": {"type": "string"}, "name": {"type": "string"}, "subject": {"$ref": "#/definitions/models.Subject"}, "subjectID": {"type": "integer"}, "updatedAt": {"type": "string"}}}, "models.FormulaCardForCreate": {"type": "object", "required": ["image_url", "name"], "properties": {"image_url": {"type": "string"}, "name": {"type": "string"}}}, "models.FormulaCardSummary": {"type": "object", "properties": {"id": {"type": "integer"}, "image_url": {"type": "string"}, "name": {"type": "string"}, "subject_name": {"type": "string"}}}, "models.FormulaCardsBySubject": {"type": "object", "properties": {"formula_cards": {"type": "array", "items": {"$ref": "#/definitions/models.FormulaCardSummary"}}, "subject_name": {"type": "string"}}}, "models.FormulaCardsForCreate": {"type": "object", "required": ["formula_cards", "subject_name"], "properties": {"formula_cards": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/models.FormulaCardForCreate"}}, "subject_name": {"type": "string"}}}, "models.MaterialForCreate": {"type": "object", "properties": {"chapterName": {"type": "string"}, "displayName": {"type": "string"}, "name": {"type": "string"}, "url": {"type": "string"}}}, "models.Option": {"type": "object", "properties": {"createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "id": {"type": "integer"}, "isCorrect": {"description": "Correctness flag", "type": "boolean"}, "optionImageURL": {"description": "Optional image URL", "type": "string"}, "optionText": {"description": "Option content", "type": "string"}, "question": {"description": "GORM association", "allOf": [{"$ref": "#/definitions/models.Question"}]}, "questionID": {"description": "Foreign key to Questions", "type": "integer"}, "updatedAt": {"type": "string"}}}, "models.OptionForCreate": {"type": "object", "required": ["option_text"], "properties": {"is_correct": {"type": "boolean"}, "option_image_url": {"type": "string"}, "option_text": {"type": "string"}}}, "models.PreviousYearPaper": {"type": "object", "properties": {"createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "examType": {"type": "string"}, "id": {"type": "integer"}, "month": {"type": "integer"}, "pdfUrl": {"type": "string"}, "updatedAt": {"type": "string"}, "year": {"type": "integer"}}}, "models.PreviousYearPaperForCreate": {"type": "object", "required": ["exam_type", "month", "pdf_url", "year"], "properties": {"exam_type": {"type": "string", "enum": ["IIT-JEE", "NEET"]}, "month": {"type": "integer", "maximum": 12, "minimum": 1}, "pdf_url": {"type": "string"}, "year": {"type": "integer", "maximum": 2100, "minimum": 1900}}}, "models.PreviousYearPapersForCreate": {"type": "object", "required": ["papers"], "properties": {"papers": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/models.PreviousYearPaperForCreate"}}}}, "models.Question": {"type": "object", "properties": {"correctAnswer": {"type": "string"}, "createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "difficulty": {"$ref": "#/definitions/models.Difficulty"}, "difficultyID": {"type": "integer"}, "fileUrl": {"type": "string"}, "id": {"type": "integer"}, "imageUrl": {"type": "string"}, "options": {"description": "One-to-many relationship with options", "type": "array", "items": {"$ref": "#/definitions/models.Option"}}, "questionType": {"type": "string"}, "text": {"type": "string"}, "topic": {"$ref": "#/definitions/models.Topic"}, "topicID": {"type": "integer"}, "updatedAt": {"type": "string"}}}, "models.QuestionForCreate": {"type": "object", "required": ["difficulty_name", "question_type", "text", "topic_name"], "properties": {"correct_answer": {"type": "string"}, "difficulty_name": {"type": "string"}, "file_url": {"type": "string"}, "image_url": {"type": "string"}, "options": {"type": "array", "items": {"$ref": "#/definitions/models.OptionForCreate"}}, "question_type": {"type": "string"}, "text": {"type": "string"}, "topic_name": {"type": "string"}}}, "models.Section": {"type": "object", "properties": {"createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "displayName": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "questions": {"type": "array", "items": {"$ref": "#/definitions/models.Question"}}, "sectionType": {"$ref": "#/definitions/models.SectionType"}, "sectionTypeID": {"type": "integer"}, "test": {"$ref": "#/definitions/models.Test"}, "testID": {"type": "integer"}, "updatedAt": {"type": "string"}}}, "models.SectionForCreate": {"type": "object", "properties": {"displayName": {"type": "string"}, "name": {"type": "string"}, "sectionTypeName": {"type": "string"}}}, "models.SectionType": {"type": "object", "properties": {"createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "id": {"type": "integer"}, "name": {"type": "string"}, "negativeMarks": {"type": "number"}, "positiveMarks": {"type": "number"}, "questionCount": {"type": "integer"}, "subject": {"$ref": "#/definitions/models.Subject"}, "subjectID": {"type": "integer"}, "updatedAt": {"type": "string"}}}, "models.SectionTypeForCreate": {"type": "object", "properties": {"name": {"type": "string"}, "negativeMarks": {"type": "number"}, "positiveMarks": {"type": "number"}, "questionCount": {"type": "integer"}, "subjectName": {"type": "string"}}}, "models.Student": {"type": "object", "properties": {"courses": {"type": "array", "items": {"$ref": "#/definitions/models.Course"}}, "createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "id": {"type": "integer"}, "parent_email": {"type": "string"}, "parent_phone": {"type": "string"}, "updatedAt": {"type": "string"}, "user": {"$ref": "#/definitions/models.User"}, "userID": {"type": "integer"}}}, "models.StudentEvaluationResult": {"type": "object", "properties": {"correct_answers": {"type": "integer"}, "evaluation_time": {"type": "string"}, "message": {"type": "string"}, "student_id": {"type": "integer"}, "student_name": {"type": "string"}, "total_questions": {"type": "integer"}, "total_score": {"type": "integer"}}}, "models.StudentForCreate": {"type": "object", "properties": {"contactAddress": {"type": "string"}, "email": {"type": "string"}, "fullName": {"type": "string"}, "parent_email": {"type": "string"}, "parent_phone": {"type": "string"}, "phoneNumber": {"type": "string"}}}, "models.StudentRankingInfo": {"type": "object", "properties": {"final_marks": {"type": "integer"}, "percentile": {"type": "number"}, "rank": {"type": "integer"}, "student_email": {"type": "string"}, "student_id": {"type": "integer"}, "student_name": {"type": "string"}, "total_negative_marks": {"type": "integer"}, "total_positive_marks": {"type": "integer"}}}, "models.StudyMaterial": {"type": "object", "properties": {"chapterID": {"type": "integer"}, "createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "displayName": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "updatedAt": {"type": "string"}, "url": {"type": "string"}}}, "models.Subject": {"type": "object", "properties": {"chapters": {"type": "array", "items": {"$ref": "#/definitions/models.Chapter"}}, "createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "displayName": {"type": "string"}, "formulaCards": {"type": "array", "items": {"$ref": "#/definitions/models.FormulaCard"}}, "id": {"type": "integer"}, "name": {"type": "string"}, "updatedAt": {"type": "string"}}}, "models.SubjectForCreate": {"type": "object", "properties": {"displayName": {"type": "string"}, "name": {"type": "string"}}}, "models.Test": {"type": "object", "properties": {"active": {"type": "boolean"}, "createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "description": {"type": "string"}, "fromTime": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "sections": {"type": "array", "items": {"$ref": "#/definitions/models.Section"}}, "testType": {"$ref": "#/definitions/models.TestType"}, "testTypeID": {"type": "integer"}, "toTime": {"type": "string"}, "updatedAt": {"type": "string"}}}, "models.TestEvaluationRequest": {"type": "object", "required": ["test_id"], "properties": {"test_id": {"type": "integer"}}}, "models.TestEvaluationResult": {"type": "object", "properties": {"message": {"type": "string"}, "student_results": {"type": "array", "items": {"$ref": "#/definitions/models.StudentEvaluationResult"}}, "test_id": {"type": "integer"}, "test_name": {"type": "string"}, "total_students_evaluated": {"type": "integer"}}}, "models.TestForCreate": {"type": "object", "properties": {"description": {"type": "string"}, "fromTime": {"type": "string"}, "name": {"type": "string"}, "sections": {"type": "array", "items": {"$ref": "#/definitions/models.SectionForCreate"}}, "testTypeName": {"type": "string"}, "toTime": {"type": "string"}}}, "models.TestRankingResult": {"type": "object", "properties": {"average_marks": {"type": "number"}, "highest_marks": {"type": "integer"}, "lowest_marks": {"type": "integer"}, "message": {"type": "string"}, "student_rankings": {"type": "array", "items": {"$ref": "#/definitions/models.StudentRankingInfo"}}, "test_id": {"type": "integer"}, "test_name": {"type": "string"}, "total_students": {"type": "integer"}}}, "models.TestResponse": {"type": "object", "properties": {"calculatedScore": {"description": "Nullable, can be set after evaluation", "type": "integer"}, "createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "id": {"type": "integer"}, "isCorrect": {"description": "Automatically evaluated", "type": "boolean"}, "question": {"$ref": "#/definitions/models.Question"}, "questionID": {"description": "FK to questions", "type": "integer"}, "responseText": {"description": "Nullable for text answers", "type": "string"}, "selectedOptionIDs": {"description": "PostgreSQL array type (use pgx or pq)", "type": "array", "items": {"type": "integer"}}, "student": {"description": "Assumes you have a Student model", "allOf": [{"$ref": "#/definitions/models.Student"}]}, "studentID": {"description": "FK to students", "type": "integer"}, "test": {"$ref": "#/definitions/models.Test"}, "testID": {"description": "FK to tests", "type": "integer"}, "updatedAt": {"type": "string"}}}, "models.TestResponseForCreate": {"type": "object", "required": ["question_id"], "properties": {"question_id": {"type": "integer"}, "response_text": {"type": "string"}, "selected_option_ids": {"type": "array", "items": {"type": "integer"}}}}, "models.TestResponseResult": {"type": "object", "properties": {"calculated_score": {"type": "integer"}, "is_correct": {"type": "boolean"}, "message": {"type": "string"}, "question_id": {"type": "integer"}}}, "models.TestResponsesForCreate": {"type": "object", "required": ["responses", "test_id"], "properties": {"responses": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/models.TestResponseForCreate"}}, "test_id": {"type": "integer"}}}, "models.TestResponsesResult": {"type": "object", "properties": {"correct_answers": {"type": "integer"}, "message": {"type": "string"}, "response_results": {"type": "array", "items": {"$ref": "#/definitions/models.TestResponseResult"}}, "student_id": {"type": "integer"}, "test_id": {"type": "integer"}, "total_questions": {"type": "integer"}, "total_score": {"type": "integer"}}}, "models.TestType": {"type": "object", "properties": {"createdAt": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "sectionTypes": {"type": "array", "items": {"$ref": "#/definitions/models.SectionType"}}, "updatedAt": {"type": "string"}}}, "models.TestTypeForCreate": {"type": "object", "properties": {"name": {"type": "string"}, "sectionTypeNames": {"type": "array", "items": {"type": "string"}}}}, "models.Topic": {"type": "object", "properties": {"chapter": {"$ref": "#/definitions/models.Chapter"}, "chapterID": {"type": "integer"}, "createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "id": {"type": "integer"}, "name": {"type": "string"}, "questions": {"type": "array", "items": {"$ref": "#/definitions/models.Question"}}, "updatedAt": {"type": "string"}}}, "models.TopicForCreate": {"type": "object", "properties": {"chapterName": {"type": "string"}, "name": {"type": "string"}}}, "models.UpdatePassword": {"type": "object", "properties": {"new_password": {"type": "string"}}}, "models.User": {"type": "object", "properties": {"contactAddress": {"type": "string"}, "createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "email": {"type": "string"}, "emailVerified": {"type": "boolean"}, "fullName": {"type": "string"}, "id": {"type": "integer"}, "passwordHash": {"type": "string"}, "phoneNumber": {"type": "string"}, "phoneVerified": {"type": "boolean"}, "role": {"type": "string"}, "updatedAt": {"type": "string"}}}, "models.Video": {"type": "object", "properties": {"chapterID": {"type": "integer"}, "createdAt": {"type": "string"}, "deletedAt": {"$ref": "#/definitions/gorm.DeletedAt"}, "displayName": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "updatedAt": {"type": "string"}, "videoUrl": {"type": "string"}, "viewCount": {"type": "integer"}}}, "models.VideoForCreate": {"type": "object", "properties": {"chapterName": {"type": "string"}, "displayName": {"type": "string"}, "name": {"type": "string"}, "videoUrl": {"type": "string"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}