[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (3 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (3 handlers)
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (4 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (4 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (4 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (4 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (4 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (4 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (4 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (4 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (4 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (4 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (4 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (4 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (4 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (4 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (4 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (4 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (4 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (4 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (3 handlers)
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-06-07T22:41:11.502+05:30 level=INFO msg="http: TLS handshake error from [::1]:57111: remote error: tls: unknown certificate"
[GIN] 2025/06/07 - 22:41:11 | 200 |       635.8µs |             ::1 | GET      "/swagger/index.html"
[GIN] 2025/06/07 - 22:41:11 | 200 |       525.7µs |             ::1 | GET      "/swagger/swagger-ui.css"
[GIN] 2025/06/07 - 22:41:11 | 200 |      2.6534ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
[GIN] 2025/06/07 - 22:41:11 | 200 |      6.2697ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
[GIN] 2025/06/07 - 22:41:11 | 200 |      1.0636ms |             ::1 | GET      "/swagger/doc.json"
[GIN] 2025/06/07 - 22:41:11 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
[GIN] 2025/06/07 - 22:41:35 | 200 |    184.7741ms |             ::1 | POST     "/api/login"
[GIN] 2025/06/07 - 22:47:23 | 200 |     15.2442ms |             ::1 | POST     "/api/subjects"
[GIN] 2025/06/07 - 22:47:36 | 200 |     14.7479ms |             ::1 | POST     "/api/section-types"
[GIN] 2025/06/07 - 22:48:12 | 200 |      1.2405ms |             ::1 | POST     "/api/subjects"
[GIN] 2025/06/07 - 22:48:42 | 200 |      2.6321ms |             ::1 | POST     "/api/subjects"
[GIN] 2025/06/07 - 22:49:48 | 200 |       6.352ms |             ::1 | POST     "/api/subjects"
[GIN] 2025/06/07 - 22:50:17 | 200 |      3.0338ms |             ::1 | POST     "/api/section-types"
[GIN] 2025/06/07 - 22:50:34 | 200 |      3.3961ms |             ::1 | POST     "/api/section-types"
[GIN] 2025/06/07 - 22:51:17 | 200 |      2.2885ms |             ::1 | POST     "/api/section-types"
[GIN] 2025/06/07 - 22:52:26 | 200 |     24.0016ms |             ::1 | POST     "/api/test-types"
time=2025-06-07T23:01:06.313+05:30 level=INFO msg="http: TLS handshake error from [::1]:57998: remote error: tls: unknown certificate"
[GIN] 2025/06/07 - 23:01:06 | 500 |      8.7825ms |             ::1 | POST     "/api/tests"
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (3 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (3 handlers)
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (4 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (4 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (4 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (4 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (4 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (4 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (4 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (4 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (4 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (4 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (4 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (4 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (4 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (4 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (4 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (4 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (4 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (4 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (3 handlers)
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-06-07T23:08:51.210+05:30 level=INFO msg="http: TLS handshake error from [::1]:58363: remote error: tls: unknown certificate"
time=2025-06-07T23:08:51.215+05:30 level=INFO msg="http: TLS handshake error from [::1]:58364: remote error: tls: unknown certificate"
[GIN] 2025/06/07 - 23:08:51 | 200 |       545.1µs |             ::1 | GET      "/swagger/index.html"
[GIN] 2025/06/07 - 23:08:51 | 200 |      1.8224ms |             ::1 | GET      "/swagger/swagger-ui.css"
[GIN] 2025/06/07 - 23:08:51 | 200 |      2.3464ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
[GIN] 2025/06/07 - 23:08:51 | 200 |      4.4628ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
[GIN] 2025/06/07 - 23:08:51 | 200 |       489.2µs |             ::1 | GET      "/swagger/doc.json"
[GIN] 2025/06/07 - 23:08:51 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
[GIN] 2025/06/07 - 23:09:41 | 200 |     193.871ms |             ::1 | POST     "/api/login"
time=2025-06-07T23:10:10.026+05:30 level=ERROR msg="Failed to parse token" error="token contains an invalid number of segments"
[GIN] 2025/06/07 - 23:10:10 | 401 |            0s |             ::1 | POST     "/api/tests"
[GIN] 2025/06/07 - 23:10:41 | 200 |     29.7405ms |             ::1 | POST     "/api/tests"
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (3 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (3 handlers)
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (4 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (4 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (4 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (4 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (4 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (4 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (4 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (4 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (4 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (4 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (4 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (4 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (4 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (4 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (4 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (4 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (4 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (4 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (3 handlers)
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-06-07T23:31:31.531+05:30 level=INFO msg="http: TLS handshake error from [::1]:59142: remote error: tls: unknown certificate"
[GIN] 2025/06/07 - 23:31:31 | 200 |       526.7µs |             ::1 | GET      "/swagger/index.html"
[GIN] 2025/06/07 - 23:31:31 | 200 |        1.03ms |             ::1 | GET      "/swagger/swagger-ui.css"
[GIN] 2025/06/07 - 23:31:31 | 200 |      2.0848ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
[GIN] 2025/06/07 - 23:31:31 | 200 |      4.3126ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
[GIN] 2025/06/07 - 23:31:31 | 200 |         506µs |             ::1 | GET      "/swagger/doc.json"
[GIN] 2025/06/07 - 23:31:31 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (3 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (3 handlers)
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (4 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (4 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (4 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (4 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (4 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (4 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (4 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (4 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (4 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (4 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (4 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (4 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (4 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (4 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (4 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (4 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (4 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (4 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (3 handlers)
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-06-07T23:32:51.758+05:30 level=INFO msg="http: TLS handshake error from [::1]:59236: remote error: tls: unknown certificate"
[GIN] 2025/06/07 - 23:32:51 | 200 |       597.7µs |             ::1 | GET      "/swagger/index.html"
[GIN] 2025/06/07 - 23:32:51 | 200 |      1.0206ms |             ::1 | GET      "/swagger/swagger-ui.css"
[GIN] 2025/06/07 - 23:32:51 | 200 |      2.3453ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
[GIN] 2025/06/07 - 23:32:51 | 200 |      5.0919ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
[GIN] 2025/06/07 - 23:32:52 | 200 |         521µs |             ::1 | GET      "/swagger/doc.json"
[GIN] 2025/06/07 - 23:32:52 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (3 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (3 handlers)
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (4 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (4 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (4 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (4 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (4 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (4 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (4 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (4 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (4 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (4 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (4 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (4 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (4 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (4 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (4 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (4 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (4 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (4 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (3 handlers)
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-06-07T23:33:42.128+05:30 level=INFO msg="http: TLS handshake error from [::1]:59307: remote error: tls: unknown certificate"
time=2025-06-07T23:33:42.133+05:30 level=INFO msg="http: TLS handshake error from [::1]:59308: remote error: tls: unknown certificate"
[GIN] 2025/06/07 - 23:33:42 | 200 |            0s |             ::1 | GET      "/swagger/index.html"
[GIN] 2025/06/07 - 23:33:42 | 200 |         617µs |             ::1 | GET      "/swagger/swagger-ui.css"
[GIN] 2025/06/07 - 23:33:42 | 200 |       2.123ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
[GIN] 2025/06/07 - 23:33:42 | 200 |       4.138ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
[GIN] 2025/06/07 - 23:33:42 | 200 |       506.8µs |             ::1 | GET      "/swagger/doc.json"
[GIN] 2025/06/07 - 23:33:42 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
[GIN] 2025/06/07 - 23:34:03 | 200 |    199.8627ms |             ::1 | POST     "/api/login"
[GIN] 2025/06/07 - 23:36:48 | 500 |     10.7875ms |             ::1 | POST     "/api/chapters"
[GIN] 2025/06/07 - 23:37:28 | 200 |      5.9192ms |             ::1 | POST     "/api/chapters"
[GIN] 2025/06/07 - 23:38:10 | 200 |      9.2226ms |             ::1 | POST     "/api/topics"
time=2025-06-07T23:51:21.813+05:30 level=INFO msg="http: TLS handshake error from [::1]:59992: remote error: tls: unknown certificate"
[GIN] 2025/06/07 - 23:51:21 | 200 |     12.3968ms |             ::1 | POST     "/api/questions"
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (3 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (3 handlers)
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (4 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (4 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (4 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (4 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (4 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (4 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (4 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (4 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (4 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (4 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (4 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (4 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (4 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (4 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (4 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (4 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (4 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (4 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (3 handlers)
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-06-08T00:02:40.072+05:30 level=INFO msg="http: TLS handshake error from [::1]:60529: remote error: tls: unknown certificate"
[GIN] 2025/06/08 - 00:02:40 | 200 |       516.2µs |             ::1 | GET      "/swagger/index.html"
[GIN] 2025/06/08 - 00:02:40 | 200 |      1.5837ms |             ::1 | GET      "/swagger/swagger-ui.css"
[GIN] 2025/06/08 - 00:02:40 | 200 |      2.6299ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
[GIN] 2025/06/08 - 00:02:40 | 200 |      4.1769ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
[GIN] 2025/06/08 - 00:02:40 | 200 |       620.3µs |             ::1 | GET      "/swagger/doc.json"
[GIN] 2025/06/08 - 00:02:40 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-06-08T00:02:55.707+05:30 level=ERROR msg="Failed to parse token" error="token contains an invalid number of segments"
[GIN] 2025/06/08 - 00:02:55 | 401 |            0s |             ::1 | POST     "/api/questions"
[GIN] 2025/06/08 - 00:03:24 | 200 |    219.5565ms |             ::1 | POST     "/api/login"
[GIN] 2025/06/08 - 00:03:56 | 500 |      2.8197ms |             ::1 | POST     "/api/questions"
time=2025-06-08T00:05:22.757+05:30 level=INFO msg="http: TLS handshake error from [::1]:60637: remote error: tls: unknown certificate"
[GIN] 2025/06/08 - 00:05:22 | 500 |      2.6967ms |             ::1 | POST     "/api/questions"
[GIN] 2025/06/08 - 00:05:52 | 500 |      1.1128ms |             ::1 | POST     "/api/questions"
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (3 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (3 handlers)
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (4 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (4 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (4 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (4 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (4 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (4 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (4 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (4 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (4 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (4 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (4 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (4 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (4 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (4 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (4 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (4 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (4 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (4 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (3 handlers)
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-06-08T00:10:03.862+05:30 level=INFO msg="http: TLS handshake error from [::1]:60841: remote error: tls: unknown certificate"
[GIN] 2025/06/08 - 00:10:04 | 200 |    203.1165ms |             ::1 | POST     "/api/login"
[GIN] 2025/06/08 - 00:10:26 | 200 |     15.9526ms |             ::1 | POST     "/api/questions"
[GIN] 2025/06/08 - 00:15:34 | 200 |     12.0689ms |             ::1 | GET      "/api/questions?topic=P%20block%20elements&difficulty=easy"
[GIN] 2025/06/08 - 00:18:05 | 200 |      1.5521ms |             ::1 | GET      "/api/questions?topic=P%20block%20elements&difficulty=medium"
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (3 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (3 handlers)
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (4 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (4 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (4 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (4 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (4 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (4 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (4 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (4 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (4 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (4 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (4 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (4 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (4 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (4 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (4 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (4 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (4 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (4 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (4 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (3 handlers)
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-06-09T21:57:14.545+05:30 level=INFO msg="http: TLS handshake error from [::1]:58365: remote error: tls: unknown certificate"
[GIN] 2025/06/09 - 21:57:14 | 200 |       1.143ms |             ::1 | GET      "/swagger/index.html"
[GIN] 2025/06/09 - 21:57:14 | 200 |      1.0159ms |             ::1 | GET      "/swagger/swagger-ui.css"
[GIN] 2025/06/09 - 21:57:14 | 200 |      1.6504ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
[GIN] 2025/06/09 - 21:57:14 | 200 |     17.9155ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
[GIN] 2025/06/09 - 21:57:15 | 200 |      6.3638ms |             ::1 | GET      "/swagger/doc.json"
[GIN] 2025/06/09 - 21:57:15 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (3 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (3 handlers)
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (4 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (4 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (4 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (4 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (4 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (4 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (4 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (4 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (4 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (4 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (4 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (4 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (4 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (4 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (4 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (4 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (4 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (4 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (4 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (4 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (4 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (3 handlers)
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-06-09T22:00:34.220+05:30 level=INFO msg="http: TLS handshake error from [::1]:58482: remote error: tls: unknown certificate"
time=2025-06-09T22:00:34.226+05:30 level=INFO msg="http: TLS handshake error from [::1]:58483: remote error: tls: unknown certificate"
[GIN] 2025/06/09 - 22:00:34 | 200 |            0s |             ::1 | GET      "/swagger/index.html"
[GIN] 2025/06/09 - 22:00:34 | 200 |      2.5716ms |             ::1 | GET      "/swagger/swagger-ui.css"
[GIN] 2025/06/09 - 22:00:34 | 200 |      3.7003ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
[GIN] 2025/06/09 - 22:00:34 | 200 |      5.7738ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
[GIN] 2025/06/09 - 22:00:34 | 200 |      1.1356ms |             ::1 | GET      "/swagger/doc.json"
[GIN] 2025/06/09 - 22:00:34 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
[GIN] 2025/06/09 - 22:00:50 | 200 |    211.8099ms |             ::1 | POST     "/api/login"
[GIN] 2025/06/09 - 22:01:20 | 200 |      14.051ms |             ::1 | GET      "/api/subjects"
time=2025-06-09T22:02:15.387+05:30 level=INFO msg="Error retrieving chapter: record not found"
[GIN] 2025/06/09 - 22:02:15 | 500 |      4.0194ms |             ::1 | GET      "/api/content?chapter_id=44"
[GIN] 2025/06/09 - 22:03:07 | 200 |      1.6865ms |             ::1 | GET      "/api/chapters?subject_id=49"
[GIN] 2025/06/09 - 22:03:13 | 200 |      1.0166ms |             ::1 | GET      "/api/chapters?subject_id=50"
time=2025-06-09T22:04:38.706+05:30 level=INFO msg="http: TLS handshake error from [::1]:58624: remote error: tls: unknown certificate"
time=2025-06-09T22:04:38.713+05:30 level=INFO msg="Error retrieving subject: record not found"
[GIN] 2025/06/09 - 22:04:38 | 500 |      1.0226ms |             ::1 | GET      "/api/chapters?subject_id=148"
[GIN] 2025/06/09 - 22:06:37 | 200 |      7.8009ms |             ::1 | POST     "/api/subjects"
[GIN] 2025/06/09 - 22:08:17 | 500 |      2.7277ms |             ::1 | POST     "/api/chapters"
time=2025-06-09T22:08:46.084+05:30 level=INFO msg="http: TLS handshake error from [::1]:58793: remote error: tls: unknown certificate"
[GIN] 2025/06/09 - 22:08:46 | 500 |     12.2089ms |             ::1 | POST     "/api/chapters"
time=2025-06-14T14:55:29.934+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-06-14T14:55:29.935+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-06-14T14:55:29.935+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-06-14T14:55:29.935+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-06-14T14:55:29.935+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-06-14T14:55:29.935+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-06-14T14:55:29.935+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-06-14T14:55:47.480+05:30 level=INFO msg="http: TLS handshake error from [::1]:56993: remote error: tls: unknown certificate"
time=2025-06-14T14:55:50.286+05:30 level=INFO msg="http: TLS handshake error from [::1]:57005: remote error: tls: unknown certificate"
time=2025-06-14T14:55:50.291+05:30 level=INFO msg="http: TLS handshake error from [::1]:57006: remote error: tls: unknown certificate"
time=2025-06-14T14:57:08.502+05:30 level=INFO msg="http: TLS handshake error from [::1]:57036: remote error: tls: unknown certificate"
time=2025-06-14T14:57:08.506+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T14:57:08.507+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/06/14 - 14:57:08 | 200 |       563.5µs |             ::1 | GET      "/swagger/index.html"
time=2025-06-14T14:57:08.531+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T14:57:08.532+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=0 response_size=144966
[GIN] 2025/06/14 - 14:57:08 | 200 |         506µs |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-06-14T14:57:08.535+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T14:57:08.535+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T14:57:08.537+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=2 response_size=312217
[GIN] 2025/06/14 - 14:57:08 | 200 |      2.0617ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-06-14T14:57:08.539+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=4 response_size=1061579
[GIN] 2025/06/14 - 14:57:08 | 200 |         4.1ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-06-14T14:57:08.675+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T14:57:08.676+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=0 response_size=70034
[GIN] 2025/06/14 - 14:57:08 | 200 |      1.0497ms |             ::1 | GET      "/swagger/doc.json"
time=2025-06-14T14:57:08.843+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T14:57:08.843+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/06/14 - 14:57:08 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-06-14T14:58:16.707+05:30 level=INFO msg="Request started" method=POST path=/api/login client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T14:58:16.707+05:30 level=INFO msg="Login attempt" email=<EMAIL> client_ip=::1
time=2025-06-14T14:58:16.935+05:30 level=INFO msg="Login successful" email=<EMAIL> user_id=21 client_ip=::1 duration_ms=228
time=2025-06-14T14:58:16.935+05:30 level=INFO msg="Request completed" method=POST path=/api/login client_ip=::1 status_code=200 duration_ms=229 response_size=313
[GIN] 2025/06/14 - 14:58:16 | 200 |    229.0137ms |             ::1 | POST     "/api/login"
time=2025-06-14T14:59:02.409+05:30 level=INFO msg="Request started" method=GET path=/api/subjects client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T14:59:02.425+05:30 level=INFO msg="Request completed" method=GET path=/api/subjects client_ip=::1 status_code=200 duration_ms=16 response_size=568
[GIN] 2025/06/14 - 14:59:02 | 200 |     16.0228ms |             ::1 | GET      "/api/subjects"
time=2025-06-14T14:59:28.277+05:30 level=INFO msg="Request started" method=GET path=/api/chapters client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T14:59:28.281+05:30 level=INFO msg="Error retrieving subject: StudyMaterials: unsupported relations for schema Subject"
time=2025-06-14T14:59:28.281+05:30 level=ERROR msg="Request completed" method=GET path=/api/chapters client_ip=::1 status_code=500 duration_ms=3 response_size=68
[GIN] 2025/06/14 - 14:59:28 | 500 |      3.2521ms |             ::1 | GET      "/api/chapters?subject_id=44"
time=2025-06-14T15:05:32.444+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-06-14T15:05:32.445+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-06-14T15:05:32.445+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-06-14T15:05:32.445+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-06-14T15:05:32.445+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-06-14T15:05:32.445+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-06-14T15:05:32.445+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-06-14T15:05:37.026+05:30 level=INFO msg="http: TLS handshake error from [::1]:57446: remote error: tls: unknown certificate"
time=2025-06-14T15:05:37.033+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:05:37.034+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/06/14 - 15:05:37 | 200 |       503.4µs |             ::1 | GET      "/swagger/index.html"
time=2025-06-14T15:05:37.050+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:05:37.051+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=0 response_size=144966
[GIN] 2025/06/14 - 15:05:37 | 200 |       507.4µs |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-06-14T15:05:37.055+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:05:37.055+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:05:37.057+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=2 response_size=312217
[GIN] 2025/06/14 - 15:05:37 | 200 |      2.4253ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-06-14T15:05:37.059+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=3 response_size=1061579
[GIN] 2025/06/14 - 15:05:37 | 200 |       4.498ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-06-14T15:05:37.206+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:05:37.207+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=0 response_size=70034
[GIN] 2025/06/14 - 15:05:37 | 200 |       898.2µs |             ::1 | GET      "/swagger/doc.json"
time=2025-06-14T15:05:37.213+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:05:37.213+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/06/14 - 15:05:37 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-06-14T15:05:58.142+05:30 level=INFO msg="Request started" method=POST path=/api/login client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:05:58.143+05:30 level=INFO msg="Login attempt" email=<EMAIL> client_ip=::1
time=2025-06-14T15:05:58.338+05:30 level=INFO msg="Login successful" email=<EMAIL> user_id=21 client_ip=::1 duration_ms=195
time=2025-06-14T15:05:58.339+05:30 level=INFO msg="Request completed" method=POST path=/api/login client_ip=::1 status_code=200 duration_ms=196 response_size=313
[GIN] 2025/06/14 - 15:05:58 | 200 |     196.586ms |             ::1 | POST     "/api/login"
time=2025-06-14T15:06:45.946+05:30 level=INFO msg="Request started" method=GET path=/api/subjects client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:06:45.966+05:30 level=INFO msg="Request completed" method=GET path=/api/subjects client_ip=::1 status_code=200 duration_ms=20 response_size=568
[GIN] 2025/06/14 - 15:06:45 | 200 |     20.1449ms |             ::1 | GET      "/api/subjects"
time=2025-06-14T15:07:04.181+05:30 level=INFO msg="Request started" method=GET path=/api/chapters client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:07:04.184+05:30 level=INFO msg="Request completed" method=GET path=/api/chapters client_ip=::1 status_code=200 duration_ms=3 response_size=4
[GIN] 2025/06/14 - 15:07:04 | 200 |      3.0049ms |             ::1 | GET      "/api/chapters?subject_id=44"
time=2025-06-14T15:07:11.701+05:30 level=INFO msg="Request started" method=GET path=/api/chapters client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:07:11.702+05:30 level=INFO msg="Request completed" method=GET path=/api/chapters client_ip=::1 status_code=200 duration_ms=0 response_size=4
[GIN] 2025/06/14 - 15:07:11 | 200 |       504.8µs |             ::1 | GET      "/api/chapters?subject_id=49"
time=2025-06-14T15:07:17.930+05:30 level=INFO msg="Request started" method=GET path=/api/chapters client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:07:17.930+05:30 level=INFO msg="Request completed" method=GET path=/api/chapters client_ip=::1 status_code=200 duration_ms=0 response_size=4
[GIN] 2025/06/14 - 15:07:17 | 200 |       503.7µs |             ::1 | GET      "/api/chapters?subject_id=50"
time=2025-06-14T15:09:45.114+05:30 level=INFO msg="Request started" method=GET path=/api/chapters client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:09:45.131+05:30 level=INFO msg="Request completed" method=GET path=/api/chapters client_ip=::1 status_code=200 duration_ms=17 response_size=4
[GIN] 2025/06/14 - 15:09:45 | 200 |     17.5932ms |             ::1 | GET      "/api/chapters?subject_id=42"
time=2025-06-14T15:10:10.163+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-06-14T15:10:10.164+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-06-14T15:10:10.164+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-06-14T15:10:10.164+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-06-14T15:10:10.165+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-06-14T15:10:10.165+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-06-14T15:10:10.165+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-06-14T15:10:18.816+05:30 level=INFO msg="http: TLS handshake error from [::1]:57607: remote error: tls: unknown certificate"
time=2025-06-14T15:10:18.822+05:30 level=INFO msg="http: TLS handshake error from [::1]:57608: remote error: tls: unknown certificate"
time=2025-06-14T15:10:18.828+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:10:18.828+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/06/14 - 15:10:18 | 200 |       431.4µs |             ::1 | GET      "/swagger/index.html"
time=2025-06-14T15:10:18.841+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:10:18.841+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:10:18.841+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:10:18.842+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=1 response_size=144966
[GIN] 2025/06/14 - 15:10:18 | 200 |      1.0195ms |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-06-14T15:10:18.843+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=2 response_size=312217
[GIN] 2025/06/14 - 15:10:18 | 200 |      2.3012ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-06-14T15:10:18.845+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=4 response_size=1061579
[GIN] 2025/06/14 - 15:10:18 | 200 |      4.5202ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-06-14T15:10:18.964+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:10:18.965+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=0 response_size=70034
[GIN] 2025/06/14 - 15:10:18 | 200 |       506.8µs |             ::1 | GET      "/swagger/doc.json"
time=2025-06-14T15:10:18.975+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:10:18.975+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/06/14 - 15:10:18 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-06-14T15:10:44.753+05:30 level=INFO msg="Request started" method=POST path=/api/login client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:10:44.753+05:30 level=INFO msg="Login attempt" email=<EMAIL> client_ip=::1
time=2025-06-14T15:10:44.955+05:30 level=DEBUG msg="Retrieving student by user ID" user_id=21
time=2025-06-14T15:10:44.955+05:30 level=DEBUG msg="Student retrieved successfully" user_id=21 email=<EMAIL> duration_ms=0
time=2025-06-14T15:10:44.955+05:30 level=INFO msg="Login successful" email=<EMAIL> user_id=21 client_ip=::1 duration_ms=201
time=2025-06-14T15:10:44.955+05:30 level=INFO msg="Request completed" method=POST path=/api/login client_ip=::1 status_code=200 duration_ms=201 response_size=313
[GIN] 2025/06/14 - 15:10:44 | 200 |    201.9228ms |             ::1 | POST     "/api/login"
time=2025-06-14T15:12:13.985+05:30 level=INFO msg="Request started" method=GET path=/api/chapters client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-14T15:12:13.985+05:30 level=DEBUG msg="Authentication attempt" method=GET path=/api/chapters client_ip=::1
time=2025-06-14T15:12:13.985+05:30 level=DEBUG msg="Authentication successful" method=GET path=/api/chapters client_ip=::1 duration_ms=0
time=2025-06-14T15:12:13.995+05:30 level=INFO msg="Request completed" method=GET path=/api/chapters client_ip=::1 status_code=200 duration_ms=9 response_size=4
[GIN] 2025/06/14 - 15:12:13 | 200 |      9.5817ms |             ::1 | GET      "/api/chapters?subject_id=42"
time=2025-06-14T16:47:25.183+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-06-14T16:47:25.183+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-06-14T16:47:25.183+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-06-14T16:47:25.183+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-06-14T16:47:25.184+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-06-14T16:47:25.184+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-06-14T16:47:25.184+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-06-15T13:37:57.424+05:30 level=INFO msg="http: TLS handshake error from 127.0.0.1:52079: remote error: tls: unknown certificate"
time=2025-06-16T12:44:42.139+05:30 level=INFO msg="http: TLS handshake error from 127.0.0.1:57731: remote error: tls: unknown certificate"
time=2025-06-16T12:45:00.723+05:30 level=INFO msg="http: TLS handshake error from 127.0.0.1:58388: remote error: tls: unknown certificate"
time=2025-06-16T16:06:44.743+05:30 level=INFO msg="http: TLS handshake error from 127.0.0.1:60385: remote error: tls: unknown certificate"
time=2025-06-17T07:02:33.796+05:30 level=INFO msg="http: TLS handshake error from 127.0.0.1:50620: remote error: tls: unknown certificate"
time=2025-06-17T09:31:52.584+05:30 level=INFO msg="http: TLS handshake error from [::1]:51675: remote error: tls: unknown certificate"
time=2025-06-17T09:31:52.604+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-17T09:31:52.610+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=9 response_size=3728
[GIN] 2025/06/17 - 09:31:52 | 200 |      9.7882ms |             ::1 | GET      "/swagger/index.html"
time=2025-06-17T09:31:52.674+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-17T09:31:52.677+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=3 response_size=144966
[GIN] 2025/06/17 - 09:31:52 | 200 |      3.2305ms |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-06-17T09:31:52.679+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-17T09:31:52.679+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-17T09:31:52.683+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=4 response_size=312217
[GIN] 2025/06/17 - 09:31:52 | 200 |      4.1734ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-06-17T09:31:52.693+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=13 response_size=1061579
[GIN] 2025/06/17 - 09:31:52 | 200 |     13.7517ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-06-17T09:31:53.138+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-17T09:31:53.141+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=2 response_size=70034
[GIN] 2025/06/17 - 09:31:53 | 200 |      2.6539ms |             ::1 | GET      "/swagger/doc.json"
time=2025-06-19T19:25:28.218+05:30 level=INFO msg="http: TLS handshake error from [::1]:58403: remote error: tls: unknown certificate"
time=2025-06-19T19:25:28.223+05:30 level=INFO msg="http: TLS handshake error from [::1]:58404: remote error: tls: unknown certificate"
time=2025-06-19T19:25:28.229+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-19T19:25:28.229+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/06/19 - 19:25:28 | 200 |       463.8µs |             ::1 | GET      "/swagger/index.html"
time=2025-06-19T19:25:28.264+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-19T19:25:28.264+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-19T19:25:28.264+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-19T19:25:28.266+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=2 response_size=144966
[GIN] 2025/06/19 - 19:25:28 | 200 |      2.0348ms |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-06-19T19:25:28.268+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=4 response_size=312217
[GIN] 2025/06/19 - 19:25:28 | 200 |      4.3805ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-06-19T19:25:28.271+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=7 response_size=1061579
[GIN] 2025/06/19 - 19:25:28 | 200 |      7.9166ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-06-19T19:25:28.448+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-19T19:25:28.449+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=0 response_size=70034
[GIN] 2025/06/19 - 19:25:28 | 200 |         504µs |             ::1 | GET      "/swagger/doc.json"
time=2025-06-20T21:57:46.007+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-06-20T21:57:46.009+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-06-20T21:57:46.009+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-06-20T21:57:46.009+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCardsBySubject-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetPreviousYearPapersByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-06-20T21:57:46.010+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-06-20T21:57:46.010+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-06-20T21:57:46.010+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-06-20T21:58:03.915+05:30 level=INFO msg="http: TLS handshake error from [::1]:57085: remote error: tls: unknown certificate"
time=2025-06-20T21:58:03.919+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-20T21:58:03.919+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/06/20 - 21:58:03 | 200 |            0s |             ::1 | GET      "/swagger/index.html"
time=2025-06-20T21:58:03.952+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-20T21:58:03.953+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=0 response_size=144966
[GIN] 2025/06/20 - 21:58:03 | 200 |       503.4µs |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-06-20T21:58:03.960+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-20T21:58:03.960+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-20T21:58:03.963+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=2 response_size=312217
[GIN] 2025/06/20 - 21:58:03 | 200 |      2.3398ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-06-20T21:58:03.964+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=4 response_size=1061579
[GIN] 2025/06/20 - 21:58:03 | 200 |      4.7018ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-06-20T21:58:04.101+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-20T21:58:04.102+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=0 response_size=103370
[GIN] 2025/06/20 - 21:58:04 | 200 |       551.9µs |             ::1 | GET      "/swagger/doc.json"
time=2025-06-20T21:58:04.207+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-20T21:58:04.207+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/06/20 - 21:58:04 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-06-20T21:58:23.021+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-20T21:58:23.021+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/06/20 - 21:58:23 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-06-20T21:58:43.043+05:30 level=INFO msg="Request started" method=POST path=/api/login client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-20T21:58:43.043+05:30 level=INFO msg="Login attempt" email=<EMAIL> client_ip=::1
time=2025-06-20T21:58:43.043+05:30 level=DEBUG msg="Validating user password" email=<EMAIL>
time=2025-06-20T21:58:43.239+05:30 level=DEBUG msg="Password validation successful" email=<EMAIL> user_id=21 duration_ms=195
time=2025-06-20T21:58:43.242+05:30 level=DEBUG msg="Retrieving student by user ID" user_id=21
time=2025-06-20T21:58:43.249+05:30 level=DEBUG msg="Student retrieved successfully" user_id=21 email=<EMAIL> duration_ms=7
time=2025-06-20T21:58:43.249+05:30 level=INFO msg="Login successful" email=<EMAIL> user_id=21 client_ip=::1 duration_ms=206
time=2025-06-20T21:58:43.250+05:30 level=INFO msg="Request completed" method=POST path=/api/login client_ip=::1 status_code=200 duration_ms=206 response_size=313
[GIN] 2025/06/20 - 21:58:43 | 200 |    206.9177ms |             ::1 | POST     "/api/login"
time=2025-06-20T21:59:07.603+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-20T21:59:07.603+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/06/20 - 21:59:07 | 200 |       185.5µs |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-06-20T21:59:11.347+05:30 level=INFO msg="Request started" method=GET path=/api/courses client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-20T21:59:11.347+05:30 level=DEBUG msg="Authentication attempt" method=GET path=/api/courses client_ip=::1
time=2025-06-20T21:59:11.347+05:30 level=DEBUG msg="Authentication successful" method=GET path=/api/courses client_ip=::1 duration_ms=0
time=2025-06-20T21:59:11.347+05:30 level=DEBUG msg="Retrieving courses for user" user_id=21
time=2025-06-20T21:59:11.360+05:30 level=DEBUG msg="Retrieved courses data" user_id=21 total_courses=3 student_enrolled_courses=2
time=2025-06-20T21:59:11.360+05:30 level=INFO msg="Courses retrieved successfully" user_id=21 total_courses=3 courses_with_purchase_status=3 duration_ms=13
time=2025-06-20T21:59:11.361+05:30 level=INFO msg="Request completed" method=GET path=/api/courses client_ip=::1 status_code=200 duration_ms=14 response_size=580
[GIN] 2025/06/20 - 21:59:11 | 200 |     14.1674ms |             ::1 | GET      "/api/courses"
time=2025-06-20T21:59:47.632+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-20T21:59:47.633+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/06/20 - 21:59:47 | 200 |       653.5µs |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-06-21T11:41:45.180+05:30 level=INFO msg="http: TLS handshake error from [::1]:61461: remote error: tls: unknown certificate"
time=2025-06-21T11:41:45.185+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-21T11:41:45.186+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=0 response_size=3728
[GIN] 2025/06/21 - 11:41:45 | 200 |       524.4µs |             ::1 | GET      "/swagger/index.html"
time=2025-06-21T11:41:45.213+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-21T11:41:45.213+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-21T11:41:45.213+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-21T11:41:45.215+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=2 response_size=144966
[GIN] 2025/06/21 - 11:41:45 | 200 |      2.6451ms |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-06-21T11:41:45.217+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=4 response_size=312217
[GIN] 2025/06/21 - 11:41:45 | 200 |      4.1829ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-06-21T11:41:45.221+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=8 response_size=1061579
[GIN] 2025/06/21 - 11:41:45 | 200 |      8.1619ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-06-21T11:41:45.365+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-06-21T11:41:45.366+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=1 response_size=103370
[GIN] 2025/06/21 - 11:41:45 | 200 |      1.0264ms |             ::1 | GET      "/swagger/doc.json"
time=2025-06-21T12:16:51.217+05:30 level=INFO msg="http: TLS handshake error from 127.0.0.1:63921: remote error: tls: unknown certificate"
time=2025-07-03T22:15:05.611+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-03T22:15:05.615+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-03T22:15:05.616+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-03T22:15:05.617+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCardsBySubject-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetPreviousYearPapersByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-03T22:15:05.618+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-03T22:15:05.618+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-03T22:15:05.618+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-03T22:15:29.190+05:30 level=INFO msg="http: TLS handshake error from [::1]:57313: remote error: tls: unknown certificate"
time=2025-07-03T22:15:33.831+05:30 level=INFO msg="http: TLS handshake error from [::1]:57315: remote error: tls: unknown certificate"
time=2025-07-03T22:15:33.844+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:15:33.846+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=2 response_size=3728
[GIN] 2025/07/03 - 22:15:33 | 200 |      2.8662ms |             ::1 | GET      "/swagger/index.html"
time=2025-07-03T22:15:33.881+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:15:33.881+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:15:33.881+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:15:33.883+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=2 response_size=144966
[GIN] 2025/07/03 - 22:15:33 | 200 |      2.8586ms |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-07-03T22:15:33.884+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=3 response_size=312217
[GIN] 2025/07/03 - 22:15:33 | 200 |      3.9521ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-07-03T22:15:33.887+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=6 response_size=1061579
[GIN] 2025/07/03 - 22:15:33 | 200 |      6.0392ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-07-03T22:15:34.060+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:15:34.063+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=2 response_size=104318
[GIN] 2025/07/03 - 22:15:34 | 200 |      3.3662ms |             ::1 | GET      "/swagger/doc.json"
time=2025-07-03T22:15:34.078+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:15:34.078+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/03 - 22:15:34 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-03T22:15:43.266+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:15:43.266+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/03 - 22:15:43 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-03T22:19:06.141+05:30 level=INFO msg="Starting HTTP server" host="" port=443 ssl_cert=C:\Users\<USER>\server.crt ssl_key=C:\Users\<USER>\server.key
time=2025-07-03T22:19:06.142+05:30 level=INFO msg="Setting up HTTP router" skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

time=2025-07-03T22:19:06.142+05:30 level=INFO msg="Configuring public routes"
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (4 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (4 handlers)
time=2025-07-03T22:19:06.142+05:30 level=INFO msg="Configuring protected routes" auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCardsBySubject-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetPreviousYearPapersByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] POST   /api/test-responses/evaluate --> ziaacademy-backend/cmd/server/http.(*Handlers).EvaluateTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (4 handlers)
time=2025-07-03T22:19:06.143+05:30 level=INFO msg="HTTP router setup completed" swagger_enabled=true auth_middleware_enabled=true
time=2025-07-03T22:19:06.143+05:30 level=INFO msg="HTTP server startup initiated successfully"
time=2025-07-03T22:19:06.143+05:30 level=INFO msg="HTTP server listening" address=:443
[GIN-debug] Listening and serving HTTPS on :443
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
time=2025-07-03T22:19:16.591+05:30 level=INFO msg="http: TLS handshake error from [::1]:57353: remote error: tls: unknown certificate"
time=2025-07-03T22:19:16.600+05:30 level=INFO msg="Request started" method=GET path=/swagger/index.html client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:19:16.601+05:30 level=INFO msg="Request completed" method=GET path=/swagger/index.html client_ip=::1 status_code=200 duration_ms=1 response_size=3728
[GIN] 2025/07/03 - 22:19:16 | 200 |      1.0359ms |             ::1 | GET      "/swagger/index.html"
time=2025-07-03T22:19:16.641+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui.css client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:19:16.641+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:19:16.641+05:30 level=INFO msg="Request started" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:19:16.643+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui.css client_ip=::1 status_code=200 duration_ms=1 response_size=144966
[GIN] 2025/07/03 - 22:19:16 | 200 |      1.8805ms |             ::1 | GET      "/swagger/swagger-ui.css"
time=2025-07-03T22:19:16.644+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-standalone-preset.js client_ip=::1 status_code=200 duration_ms=2 response_size=312217
[GIN] 2025/07/03 - 22:19:16 | 200 |      2.9054ms |             ::1 | GET      "/swagger/swagger-ui-standalone-preset.js"
time=2025-07-03T22:19:16.647+05:30 level=INFO msg="Request completed" method=GET path=/swagger/swagger-ui-bundle.js client_ip=::1 status_code=200 duration_ms=6 response_size=1061579
[GIN] 2025/07/03 - 22:19:16 | 200 |      6.0613ms |             ::1 | GET      "/swagger/swagger-ui-bundle.js"
time=2025-07-03T22:19:17.078+05:30 level=INFO msg="Request started" method=GET path=/swagger/doc.json client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:19:17.082+05:30 level=INFO msg="Request completed" method=GET path=/swagger/doc.json client_ip=::1 status_code=200 duration_ms=3 response_size=104315
[GIN] 2025/07/03 - 22:19:17 | 200 |      3.1251ms |             ::1 | GET      "/swagger/doc.json"
time=2025-07-03T22:19:17.089+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:19:17.089+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/03 - 22:19:17 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-03T22:19:38.594+05:30 level=INFO msg="Request started" method=POST path=/api/login client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:19:38.595+05:30 level=INFO msg="Login attempt" email=<EMAIL> client_ip=::1
time=2025-07-03T22:19:38.595+05:30 level=DEBUG msg="Validating user password" email=<EMAIL>
time=2025-07-03T22:19:38.850+05:30 level=DEBUG msg="Password validation successful" email=<EMAIL> user_id=21 duration_ms=255
time=2025-07-03T22:19:38.850+05:30 level=DEBUG msg="Retrieving student by user ID" user_id=21
time=2025-07-03T22:19:38.864+05:30 level=DEBUG msg="Student retrieved successfully" user_id=21 email=<EMAIL> duration_ms=13
time=2025-07-03T22:19:38.864+05:30 level=INFO msg="Login successful" email=<EMAIL> user_id=21 client_ip=::1 duration_ms=269
time=2025-07-03T22:19:38.864+05:30 level=INFO msg="Request completed" method=POST path=/api/login client_ip=::1 status_code=200 duration_ms=269 response_size=313
[GIN] 2025/07/03 - 22:19:38 | 200 |    269.5993ms |             ::1 | POST     "/api/login"
time=2025-07-03T22:20:10.208+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:20:10.208+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/03 - 22:20:10 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-03T22:20:14.664+05:30 level=INFO msg="Request started" method=GET path=/swagger/favicon-32x32.png client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:20:14.664+05:30 level=INFO msg="Request completed" method=GET path=/swagger/favicon-32x32.png client_ip=::1 status_code=200 duration_ms=0 response_size=628
[GIN] 2025/07/03 - 22:20:14 | 200 |            0s |             ::1 | GET      "/swagger/favicon-32x32.png"
time=2025-07-03T22:20:18.591+05:30 level=INFO msg="Request started" method=GET path=/api/courses client_ip=::1 user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
time=2025-07-03T22:20:18.591+05:30 level=DEBUG msg="Authentication attempt" method=GET path=/api/courses client_ip=::1
time=2025-07-03T22:20:18.592+05:30 level=DEBUG msg="Authentication successful" method=GET path=/api/courses client_ip=::1 duration_ms=0
time=2025-07-03T22:20:18.592+05:30 level=DEBUG msg="Retrieving courses for user" user_id=21
time=2025-07-03T22:20:18.610+05:30 level=DEBUG msg="Retrieved courses data" user_id=21 total_courses=3 student_enrolled_courses=0
time=2025-07-03T22:20:18.610+05:30 level=INFO msg="Courses retrieved successfully" user_id=21 total_courses=3 filtered_courses_returned=0 free_courses_count=0 enrolled_paid_courses_count=0 duration_ms=18
time=2025-07-03T22:20:18.610+05:30 level=INFO msg="Request completed" method=GET path=/api/courses client_ip=::1 status_code=200 duration_ms=18 response_size=41
[GIN] 2025/07/03 - 22:20:18 | 200 |     18.8106ms |             ::1 | GET      "/api/courses"
